#!/bin/bash

# Test script for WS2812 RGB LED control
# This script demonstrates how to control individual RGB channels

echo "WS2812 RGB LED Test Script"
echo "=========================="

# Check if LED devices exist
LED_BASE_PATH="/sys/class/leds"

echo "Checking for LED devices..."
if [ -d "$LED_BASE_PATH" ]; then
    echo "Available LED devices:"
    ls -la $LED_BASE_PATH/ | grep ws-led
    echo ""
else
    echo "LED class directory not found. Make sure the driver is loaded."
    exit 1
fi

# Function to set LED color
set_led_color() {
    local led_name=$1
    local red=$2
    local green=$3
    local blue=$4
    
    echo "Setting $led_name to RGB($red, $green, $blue)"
    
    # Set individual color channels
    if [ -f "$LED_BASE_PATH/${led_name}:red/brightness" ]; then
        echo $red > "$LED_BASE_PATH/${led_name}:red/brightness"
    else
        echo "Warning: ${led_name}:red not found"
    fi
    
    if [ -f "$LED_BASE_PATH/${led_name}:green/brightness" ]; then
        echo $green > "$LED_BASE_PATH/${led_name}:green/brightness"
    else
        echo "Warning: ${led_name}:green not found"
    fi
    
    if [ -f "$LED_BASE_PATH/${led_name}:blue/brightness" ]; then
        echo $blue > "$LED_BASE_PATH/${led_name}:blue/brightness"
    else
        echo "Warning: ${led_name}:blue not found"
    fi
}

# Function to get LED color
get_led_color() {
    local led_name=$1
    
    if [ -f "$LED_BASE_PATH/${led_name}:red/brightness" ] && \
       [ -f "$LED_BASE_PATH/${led_name}:green/brightness" ] && \
       [ -f "$LED_BASE_PATH/${led_name}:blue/brightness" ]; then
        
        local red=$(cat "$LED_BASE_PATH/${led_name}:red/brightness")
        local green=$(cat "$LED_BASE_PATH/${led_name}:green/brightness")
        local blue=$(cat "$LED_BASE_PATH/${led_name}:blue/brightness")
        
        echo "$led_name current color: RGB($red, $green, $blue)"
    else
        echo "Error: RGB channels for $led_name not found"
    fi
}

# Test different colors
echo "Testing RGB color control..."
echo ""

# Test ws-led0 if it exists
if [ -f "$LED_BASE_PATH/ws-led0:red/brightness" ]; then
    echo "Testing ws-led0..."
    
    # Red
    set_led_color "ws-led0" 255 0 0
    sleep 1
    get_led_color "ws-led0"
    
    # Green  
    set_led_color "ws-led0" 0 255 0
    sleep 1
    get_led_color "ws-led0"
    
    # Blue
    set_led_color "ws-led0" 0 0 255
    sleep 1
    get_led_color "ws-led0"
    
    # White
    set_led_color "ws-led0" 255 255 255
    sleep 1
    get_led_color "ws-led0"
    
    # Purple
    set_led_color "ws-led0" 255 0 255
    sleep 1
    get_led_color "ws-led0"
    
    # Turn off
    set_led_color "ws-led0" 0 0 0
    get_led_color "ws-led0"
    
else
    echo "ws-led0 RGB channels not found. Please check if the driver is properly loaded."
fi

echo ""
echo "Test completed!"
