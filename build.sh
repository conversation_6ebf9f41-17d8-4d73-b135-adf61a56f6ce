#!/bin/bash

# WS2812 VLEDS 内核模块编译脚本
# 支持本机编译和交叉编译

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
WS2812 VLEDS 内核模块编译脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -c, --clean             清理编译文件
    -k, --kdir PATH         指定内核构建目录路径
    -a, --arch ARCH         指定目标架构 (arm, arm64, x86_64等)
    -t, --toolchain PREFIX  指定交叉编译工具链前缀
    -i, --install           编译后安装模块
    -v, --verbose           显示详细编译信息

示例:
    $0                                          # 本机编译
    $0 -c                                       # 清理编译文件
    $0 -k /usr/src/linux-headers-5.15.0        # 指定内核路径
    $0 -a arm64 -t aarch64-linux-gnu-          # ARM64交叉编译
    $0 -a arm -t arm-linux-gnueabihf- -i       # ARM32交叉编译并安装

EOF
}

# 检查依赖
check_dependencies() {
    print_info "检查编译依赖..."
    
    # 检查make
    if ! command -v make &> /dev/null; then
        print_error "make 未安装，请先安装 build-essential 或 Development Tools"
        exit 1
    fi
    
    # 检查gcc（如果是本机编译）
    if [[ -z "$CROSS_COMPILE" ]]; then
        if ! command -v gcc &> /dev/null; then
            print_error "gcc 未安装，请先安装编译工具链"
            exit 1
        fi
    else
        # 检查交叉编译工具链
        if ! command -v "${CROSS_COMPILE}gcc" &> /dev/null; then
            print_error "交叉编译工具链 ${CROSS_COMPILE}gcc 未找到"
            print_info "请确保工具链在 PATH 中，或安装对应的交叉编译工具"
            exit 1
        fi
    fi
    
    print_success "依赖检查通过"
}

# 检查内核目录
check_kernel_dir() {
    if [[ ! -d "$KDIR" ]]; then
        print_error "内核目录不存在: $KDIR"
        print_info "请安装内核开发包或指定正确的内核路径"
        exit 1
    fi
    
    if [[ ! -f "$KDIR/Makefile" ]]; then
        print_error "内核目录无效: $KDIR (缺少 Makefile)"
        exit 1
    fi
    
    print_success "内核目录检查通过: $KDIR"
}

# 显示编译配置
show_config() {
    print_info "编译配置:"
    echo "  内核目录: $KDIR"
    echo "  目标架构: ${ARCH:-本机架构}"
    echo "  工具链前缀: ${CROSS_COMPILE:-本机工具链}"
    echo "  详细输出: ${VERBOSE:-否}"
    echo "  安装模块: ${INSTALL:-否}"
    echo ""
}

# 执行编译
do_compile() {
    print_info "开始编译 WS2812 VLEDS 内核模块..."
    
    local make_args="KDIR=$KDIR"
    
    if [[ -n "$ARCH" ]]; then
        make_args="$make_args ARCH=$ARCH"
    fi
    
    if [[ -n "$CROSS_COMPILE" ]]; then
        make_args="$make_args CROSS_COMPILE=$CROSS_COMPILE"
    fi
    
    if [[ "$VERBOSE" == "yes" ]]; then
        make_args="$make_args V=1"
    fi
    
    print_info "执行: make $make_args"
    
    if make $make_args; then
        print_success "编译完成!"
        
        # 显示生成的文件
        if [[ -f "ws2812-vleds.ko" ]]; then
            print_info "生成的模块文件:"
            ls -la ws2812-vleds.ko
            
            # 显示模块信息
            print_info "模块信息:"
            modinfo ws2812-vleds.ko | head -10
        fi
    else
        print_error "编译失败!"
        exit 1
    fi
}

# 安装模块
do_install() {
    if [[ "$INSTALL" == "yes" ]]; then
        print_info "安装模块..."
        
        if [[ -n "$CROSS_COMPILE" ]]; then
            print_warning "交叉编译模式下跳过安装，请手动部署到目标设备"
            return
        fi
        
        local make_args="KDIR=$KDIR"
        if sudo make $make_args install; then
            print_success "模块安装完成"
            print_info "运行 'sudo depmod -a' 更新模块依赖"
            sudo depmod -a
        else
            print_error "模块安装失败"
            exit 1
        fi
    fi
}

# 清理编译文件
do_clean() {
    print_info "清理编译文件..."
    
    local make_args="KDIR=${KDIR:-/lib/modules/$(uname -r)/build}"
    
    if [[ -n "$ARCH" ]]; then
        make_args="$make_args ARCH=$ARCH"
    fi
    
    if [[ -n "$CROSS_COMPILE" ]]; then
        make_args="$make_args CROSS_COMPILE=$CROSS_COMPILE"
    fi
    
    if make $make_args clean; then
        print_success "清理完成"
    else
        print_error "清理失败"
        exit 1
    fi
}

# 默认值
KDIR="/lib/modules/$(uname -r)/build"
ARCH=""
CROSS_COMPILE=""
VERBOSE=""
INSTALL=""
CLEAN=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            CLEAN="yes"
            shift
            ;;
        -k|--kdir)
            KDIR="$2"
            shift 2
            ;;
        -a|--arch)
            ARCH="$2"
            shift 2
            ;;
        -t|--toolchain)
            CROSS_COMPILE="$2"
            shift 2
            ;;
        -i|--install)
            INSTALL="yes"
            shift
            ;;
        -v|--verbose)
            VERBOSE="yes"
            shift
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主流程
main() {
    print_info "WS2812 VLEDS 内核模块编译脚本"
    echo ""
    
    if [[ "$CLEAN" == "yes" ]]; then
        do_clean
        exit 0
    fi
    
    check_dependencies
    check_kernel_dir
    show_config
    do_compile
    do_install
    
    print_success "所有操作完成!"
}

# 执行主流程
main "$@"
